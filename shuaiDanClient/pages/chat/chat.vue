<template>
  <view class="chat-container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="header-left">
        <picker 
          :value="selectedGroupIndex" 
          :range="groupNames" 
          @change="handleGroupChange"
          class="group-picker"
        >
          <view class="picker-content">
            <text class="group-name">{{ currentGroupName }}</text>
            <text class="dropdown-icon">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="header-right">
        <view class="search-icon" @click="handleSearch">🔍</view>
        <view class="avatar" @click="handleProfile">👤</view>
      </view>
    </view>

    <!-- 消息列表 -->
    <scroll-view 
      class="message-list" 
      scroll-y 
      :scroll-top="scrollTop"
      @scrolltoupper="loadMoreMessages"
    >
      <view 
        v-for="message in messages" 
        :key="message.id" 
        class="message-item"
        :class="{ 'system-message': message.message_type === 'system' }"
      >
        <!-- 系统消息 -->
        <view v-if="message.message_type === 'system'" class="system-content">
          <text class="system-text">{{ message.content }}</text>
        </view>
        
        <!-- 普通消息 -->
        <view v-else class="normal-message">
          <view class="message-header">
            <text
              class="sender-name clickable"
              @click="startPrivateChatWithUser(message.sender)"
            >
              {{ message.sender.nickname }}
            </text>
            <text class="message-time">{{ formatTime(message.created_at) }}</text>
          </view>
          <view class="message-content">
            <text>{{ message.content }}</text>
          </view>
        </view>
      </view>
      
      <!-- 加载更多提示 -->
      <view v-if="isLoadingMore" class="loading-more">
        <text>加载中...</text>
      </view>
    </scroll-view>

    <!-- 功能按钮区域 -->
    <view class="function-buttons">
      <button class="func-btn" style="background-color: #8b5cf6;" @click="handleCompanyTransfer">公司转让</button>
      <button class="func-btn" style="background-color: #06b6d4;" @click="handleLocalPeers">本地同行</button>
      <button class="func-btn" style="background-color: #ef4444;" @click="handleGroupAnnouncement">群公告</button>
    </view>

    <!-- 底部输入区域 -->
    <view class="input-section">
      <view class="input-container">
        <input 
          v-model="inputMessage"
          class="message-input" 
          placeholder="请输入您的接单信息"
          @confirm="sendMessage"
          confirm-type="send"
        />
        <button class="send-btn" @click="sendMessage" :disabled="!inputMessage.trim()">发送</button>
      </view>
      <button class="private-msg-btn" @click="handlePrivateMessage">私信</button>
    </view>

    <!-- 群公告弹出框 -->
    <view v-if="showAnnouncementModal" class="modal-overlay" @click="closeAnnouncementModal">
      <view class="announcement-modal" @click.stop>
        <view class="modal-header">
          <text class="modal-title">群公告</text>
          <text class="modal-close" @click="closeAnnouncementModal">×</text>
        </view>

        <view class="modal-content">
          <!-- 群组信息 -->
          <view class="group-info-modal">
            <view class="group-avatar-small">
              <image
                v-if="currentGroupInfo.group_avatar"
                :src="currentGroupInfo.group_avatar"
                class="avatar-image-small"
              />
              <view v-else class="avatar-placeholder-small">
                <text>{{ currentGroupName.charAt(0) || '群' }}</text>
              </view>
            </view>
            <view class="group-details-modal">
              <text class="group-name-modal">{{ currentGroupName }}</text>
              <text class="member-count-modal">成员数量：{{ currentGroupInfo.member_count || 0 }}人</text>
            </view>
          </view>

          <!-- 公告内容 -->
          <view class="announcement-content-modal">
            <view v-if="currentGroupInfo.description && currentGroupInfo.description.trim()" class="announcement-text-modal">
              <text>{{ currentGroupInfo.description }}</text>
            </view>
            <view v-else class="no-announcement-modal">
              <text class="no-announcement-icon">📢</text>
              <text class="no-announcement-text">暂无群公告</text>
              <text class="no-announcement-desc">管理员还没有设置群公告</text>
            </view>
          </view>

          <!-- 更新时间 -->
          <view v-if="currentGroupInfo.created_at" class="update-time-modal">
            <text>更新时间：{{ formatTime(currentGroupInfo.created_at) }}</text>
          </view>
        </view>

        <!-- 加载状态 -->
        <view v-if="loadingAnnouncement" class="loading-modal">
          <text>加载中...</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { chatAPI, utils } from '@/utils/api.js'

// 响应式数据
// 群组相关
const groups = ref([])
const selectedGroupIndex = ref(0)
const currentGroupId = ref(1) // 默认群组ID

// 消息相关
const messages = ref([])
const inputMessage = ref('')
const lastMessageId = ref(0)
const isLoadingMore = ref(false)
const scrollTop = ref(0)

// 轮询相关
const pollingTimer = ref(null)
const pollingInterval = ref(3000) // 3秒轮询一次

// 用户信息
const userInfo = ref(null)
const isLoggedIn = ref(false)

// 群公告相关
const showAnnouncementModal = ref(false)
const currentGroupInfo = ref({})
const loadingAnnouncement = ref(false)

// 计算属性
const groupNames = computed(() => {
  return groups.value.map(group => group.group_name || '未知群组')
})

const currentGroupName = computed(() => {
  return groups.value[selectedGroupIndex.value]?.group_name || '全国接单总群'
})

// 生命周期钩子
onMounted(() => {
  initPage()
})

onUnmounted(() => {
  // 清除轮询定时器
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
  }
})

// 方法定义
/**
 * 初始化页面
 */
const initPage = async () => {
  // 检查登录状态
  checkLoginStatus()

  // 加载群组列表
  await loadGroups()

  // 加载消息
  await loadMessages()

  // 开始轮询
  startPolling()
}

/**
 * 检查登录状态
 */
const checkLoginStatus = () => {
  const token = uni.getStorageSync('token')
  const userInfoData = uni.getStorageSync('userInfo')

  isLoggedIn.value = !!token
  userInfo.value = userInfoData
}

/**
 * 加载群组列表
 */
const loadGroups = async () => {
  try {
    // 始终获取所有公开群组列表，让用户可以选择任何群组
    const result = await chatAPI.getGroups(1, 20) // 增加获取数量到20个群组
    if (result.success && result.data.data.length > 0) {
      groups.value = result.data.data.map(item => ({
        id: item.id,
        group_name: item.group_name
      }))
    } else {
      // 如果API调用失败，使用默认群组
      groups.value = [
        { id: 1, group_name: '全国接单总群' },
        { id: 2, group_name: '北京同行群' },
        { id: 3, group_name: '上海同行群' },
        { id: 4, group_name: '广州同行群' },
        { id: 5, group_name: '深圳同行群' }
      ]
    }

    currentGroupId.value = groups.value[0]?.id || 1
  } catch (error) {
    console.error('加载群组失败:', error)
    // 出错时使用默认群组
    groups.value = [
      { id: 1, group_name: '全国接单总群' }
    ]
    currentGroupId.value = 1
  }
}

/**
 * 加载消息列表
 */
const loadMessages = async () => {
  try {
    if (currentGroupId.value) {
      // 所有用户都获取真实消息数据（无论是否登录）
      const result = await chatAPI.getGroupMessages(currentGroupId.value, 1, 50)
      if (result.success && result.data.data) {
        messages.value = result.data.data.reverse() // 反转数组，最新消息在底部
        if (messages.value.length > 0) {
          lastMessageId.value = Math.max(...messages.value.map(m => m.id))
        }
      }
    } else {
      // 无群组时清空消息
      messages.value = []
      lastMessageId.value = 0
    }

    // 滚动到底部
    nextTick(() => {
      scrollToBottom()
    })
  } catch (error) {
    console.error('加载消息失败:', error)
    utils.handleError(error, '加载消息失败')

    // 如果API调用失败，显示空消息列表
    messages.value = []
    lastMessageId.value = 0
  }
}

/**
 * 发送消息
 */
const sendMessage = async () => {
  if (!inputMessage.value.trim()) return

  if (!isLoggedIn.value) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }

  const messageContent = inputMessage.value.trim()
  inputMessage.value = ''

  try {
    // 调用API发送消息
    const result = await chatAPI.sendMessage(currentGroupId.value, messageContent)

    if (result.success) {
      // 添加到消息列表
      messages.value.push(result.data)
      lastMessageId.value = result.data.id

      // 滚动到底部
      nextTick(() => {
        scrollToBottom()
      })

      uni.showToast({
        title: '发送成功',
        icon: 'success'
      })
    } else {
      throw new Error(result.message || '发送失败')
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    utils.handleError(error, '发送消息失败')

    // 恢复输入内容
    inputMessage.value = messageContent
  }
}

/**
 * 开始轮询新消息
 */
const startPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
  }

  pollingTimer.value = setInterval(() => {
    pollNewMessages()
  }, pollingInterval.value)
}

/**
 * 轮询新消息
 */
const pollNewMessages = async () => {
  try {
    if (!currentGroupId.value) {
      return
    }

    // 所有用户都可以轮询新消息（无论是否登录）
    const result = await chatAPI.getNewMessages(currentGroupId.value, lastMessageId.value)

    if (result.success && result.data.length > 0) {
      // 添加新消息到列表
      messages.value.push(...result.data)

      // 更新最后消息ID
      const newLastId = Math.max(...result.data.map(m => m.id))
      if (newLastId > lastMessageId.value) {
        lastMessageId.value = newLastId
      }

      // 滚动到底部
      nextTick(() => {
        scrollToBottom()
      })
    }
  } catch (error) {
    console.error('轮询新消息失败:', error)
    // 轮询失败不显示错误提示，避免频繁弹窗
  }
}

/**
 * 滚动到底部
 */
const scrollToBottom = () => {
  scrollTop.value = 999999
}

/**
 * 格式化时间
 */
const formatTime = (timeStr) => {
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now - time

  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return Math.floor(diff / 60000) + '分钟前'
  } else if (diff < 86400000) { // 24小时内
    return Math.floor(diff / 3600000) + '小时前'
  } else {
    return time.toLocaleDateString()
  }
}

// 事件处理方法
const handleGroupChange = (e) => {
  selectedGroupIndex.value = e.detail.value
  currentGroupId.value = groups.value[selectedGroupIndex.value]?.id
  loadMessages() // 重新加载消息
}

const handleSearch = () => {
  uni.showToast({ title: '搜索功能开发中', icon: 'none' })
}

const handleProfile = () => {
  if (isLoggedIn.value) {
    uni.navigateTo({ url: '/pages/user/profile' })
  } else {
    uni.navigateTo({ url: '/pages/login/login' })
  }
}

const handleCompanyTransfer = () => {
  uni.navigateTo({ url: '/pages/listings/list' })
}

const handleLocalPeers = () => {
  uni.showToast({ title: '本地同行功能开发中', icon: 'none' })
}



const handleGroupAnnouncement = async () => {
  try {
    loadingAnnouncement.value = true
    showAnnouncementModal.value = true

    // 获取当前群组详情
    const result = await chatAPI.getGroupDetail(currentGroupId.value)

    if (result.success) {
      currentGroupInfo.value = result.data
    } else {
      throw new Error(result.message || '获取群公告失败')
    }
  } catch (error) {
    console.error('获取群公告失败:', error)
    utils.handleError(error, '获取群公告失败')
    // 即使失败也显示弹框，显示错误信息
    currentGroupInfo.value = {
      group_name: currentGroupName.value,
      description: '获取群公告失败，请稍后重试'
    }
  } finally {
    loadingAnnouncement.value = false
  }
}

// 关闭群公告弹出框
const closeAnnouncementModal = () => {
  showAnnouncementModal.value = false
  currentGroupInfo.value = {}
}

const handlePrivateMessage = () => {
  uni.showToast({ title: '私信功能开发中', icon: 'none' })
}

// 与用户发起私聊
const startPrivateChatWithUser = async (sender) => {
  if (!sender || !sender.id) {
    uni.showToast({
      title: '用户信息不完整',
      icon: 'none'
    })
    return
  }

  // 检查是否是自己
  const currentUser = utils.getCurrentUser()
  if (currentUser && currentUser.id.toString() === sender.id.toString()) {
    uni.showToast({
      title: '不能与自己私聊',
      icon: 'none'
    })
    return
  }

  try {
    // 发起私聊
    const response = await chatAPI.startPrivateChat(sender.id)

    if (response.success) {
      // 跳转到私聊页面
      uni.navigateTo({
        url: `/pages/chat/private?userId=${sender.id}&nickname=${encodeURIComponent(sender.nickname || '用户')}`
      })
    } else {
      uni.showToast({
        title: response.message || '发起私聊失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('发起私聊错误:', error)
    uni.showToast({
      title: '发起私聊失败',
      icon: 'none'
    })
  }
}

const loadMoreMessages = () => {
  // 加载更多历史消息
  uni.showToast({ title: '加载更多功能开发中', icon: 'none' })
}
</script>

<style scoped>
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
}

.header {
  background-color: white;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #e5e5e5;
  flex-shrink: 0; /* 防止头部被压缩 */
}

.group-picker {
  flex: 1;
}

.picker-content {
  display: flex;
  align-items: center;
}

.group-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 10rpx;
}

.dropdown-icon {
  font-size: 24rpx;
  color: #666;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.search-icon, .avatar {
  font-size: 36rpx;
  padding: 10rpx;
}

.message-list {
  flex: 1;
  padding: 20rpx;
  padding-bottom: 260rpx; /* 为底部固定区域留出更多空间：功能按钮(100rpx) + 输入框(120rpx) + 额外间距(40rpx) */
  overflow-y: auto;
}

.message-item {
  margin-bottom: 20rpx;
}

.system-message {
  text-align: center;
}

.system-content {
  background-color: #f0f0f0;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  display: inline-block;
}

.system-text {
  font-size: 24rpx;
  color: #666;
}

.normal-message {
  background-color: white;
  padding: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.sender-name {
  font-size: 28rpx;
  color: #22c55e;
  font-weight: bold;
}

.sender-name.clickable {
  cursor: pointer;
  text-decoration: underline;
  opacity: 0.8;
}

.sender-name.clickable:hover {
  opacity: 1;
}

.message-time {
  font-size: 24rpx;
  color: #999;
}

.message-content {
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
}

.function-buttons {
  position: fixed;
  bottom: 120rpx; /* 在输入框上方，输入框高度约120rpx */
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx;
  gap: 10rpx;
  background-color: white;
  border-top: 1rpx solid #e5e5e5;
  z-index: 100;
}

.func-btn {
  flex: 1;
  height: 60rpx;
  font-size: 22rpx;
  color: white;
  border: none;
  border-radius: 8rpx;
  text-align: left;
  padding-left: 10rpx;
}

.input-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 20rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  border-top: 1rpx solid #e5e5e5;
  z-index: 100;
}

.input-container {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 25rpx;
  padding: 0 20rpx;
}

.message-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  border: none;
  background: transparent;
}

.send-btn {
  background-color: #22c55e;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.send-btn:disabled {
  background-color: #ccc;
}

.private-msg-btn {
  background-color: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 20rpx;
  padding: 15rpx 30rpx;
  font-size: 24rpx;
}

.loading-more {
  text-align: center;
  padding: 20rpx;
  color: #999;
  font-size: 24rpx;
}

/* 群公告弹出框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.announcement-modal {
  background-color: #fff;
  border-radius: 20rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fafafa;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
  cursor: pointer;
}

.modal-content {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.group-info-modal {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.group-avatar-small {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.avatar-image-small {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.avatar-placeholder-small {
  width: 100%;
  height: 100%;
  background-color: #e5e5e5;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #999;
  font-weight: 600;
}

.group-details-modal {
  flex: 1;
}

.group-name-modal {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.member-count-modal {
  font-size: 24rpx;
  color: #666;
}

.announcement-content-modal {
  margin-bottom: 20rpx;
}

.announcement-text-modal {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
  word-break: break-word;
}

.no-announcement-modal {
  text-align: center;
  padding: 60rpx 0;
}

.no-announcement-icon {
  font-size: 60rpx;
  display: block;
  margin-bottom: 15rpx;
}

.no-announcement-text {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.no-announcement-desc {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.update-time-modal {
  text-align: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.update-time-modal text {
  font-size: 24rpx;
  color: #999;
}

.loading-modal {
  text-align: center;
  padding: 40rpx 0;
  color: #666;
  font-size: 28rpx;
}
</style>
