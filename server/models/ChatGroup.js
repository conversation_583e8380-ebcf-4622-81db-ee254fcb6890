const { query, execute } = require('../config/db');

/**
 * 聊天群组模型
 */
class ChatGroup {
  constructor(data) {
    this.id = data.id;
    this.group_name = data.group_name;
    this.group_avatar = data.group_avatar;
    this.description = data.description;
    this.group_type = data.group_type || 'group';
    this.private_user_ids = data.private_user_ids;
    this.created_at = data.created_at;
  }

  /**
   * 创建新群组
   * @param {Object} groupData 群组数据
   * @returns {Promise<ChatGroup>}
   */
  static async create(groupData) {
    const { group_name, group_avatar, description, group_type = 'group', private_user_ids } = groupData;

    const [result] = await execute(
      'INSERT INTO chat_groups (group_name, group_avatar, description, group_type, private_user_ids) VALUES (?, ?, ?, ?, ?)',
      [group_name, group_avatar, description, group_type, private_user_ids]
    );

    const rows = await query(
      'SELECT * FROM chat_groups WHERE id = ?',
      [result.insertId]
    );

    return new ChatGroup(rows[0]);
  }

  /**
   * 根据ID查找群组
   * @param {number} id 群组ID
   * @returns {Promise<ChatGroup|null>}
   */
  static async findById(id) {
    const rows = await query(
      'SELECT * FROM chat_groups WHERE id = ?',
      [id]
    );

    return rows.length > 0 ? new ChatGroup(rows[0]) : null;
  }

  /**
   * 获取所有群组列表
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  static async getList(options = {}) {
    const { page = 1, pageSize = 20 } = options;
    const offset = (page - 1) * pageSize;

    // 获取总数
    const countRows = await query(
      'SELECT COUNT(*) as total FROM chat_groups'
    );
    const total = countRows[0].total;

    // 获取群组列表 - 使用字符串拼接而不是参数绑定（LIMIT/OFFSET的限制）
    const rows = await query(
      `SELECT * FROM chat_groups ORDER BY id ASC LIMIT ${parseInt(pageSize)} OFFSET ${parseInt(offset)}`
    );

    const groups = rows.map(row => new ChatGroup(row));

    return {
      data: groups,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }

  /**
   * 获取用户所在的群组列表
   * @param {number} userId 用户ID
   * @returns {Promise<Array<ChatGroup>>}
   */
  static async getUserGroups(userId) {
    const rows = await query(`
      SELECT cg.* FROM chat_groups cg
      INNER JOIN group_members gm ON cg.id = gm.group_id
      WHERE gm.user_id = ?
      ORDER BY gm.joined_at DESC
    `, [userId]);

    return rows.map(row => new ChatGroup(row));
  }

  /**
   * 更新群组信息
   * @param {number} id 群组ID
   * @param {Object} updateData 更新数据
   * @returns {Promise<ChatGroup|null>}
   */
  static async update(id, updateData) {
    const fields = [];
    const values = [];

    Object.keys(updateData).forEach(key => {
      if (['group_name', 'group_avatar', 'description'].includes(key)) {
        fields.push(`${key} = ?`);
        values.push(updateData[key]);
      }
    });

    if (fields.length === 0) {
      throw new Error('没有有效的更新字段');
    }

    values.push(id);

    await query(
      `UPDATE chat_groups SET ${fields.join(', ')} WHERE id = ?`,
      values
    );

    return await this.findById(id);
  }

  /**
   * 删除群组
   * @param {number} id 群组ID
   * @returns {Promise<boolean>}
   */
  static async delete(id) {
    const [result] = await execute(
      'DELETE FROM chat_groups WHERE id = ?',
      [id]
    );

    return result.affectedRows > 0;
  }

  /**
   * 创建或获取私聊群组
   * @param {number} userId1 用户1的ID
   * @param {number} userId2 用户2的ID
   * @returns {Promise<ChatGroup>}
   */
  static async createOrGetPrivateChat(userId1, userId2) {
    // 确保用户ID顺序一致（小的在前）
    const smallerId = Math.min(userId1, userId2);
    const largerId = Math.max(userId1, userId2);
    const privateUserIds = `${smallerId}_${largerId}`;

    // 先查找是否已存在私聊群组
    const existingRows = await query(
      'SELECT * FROM chat_groups WHERE group_type = ? AND private_user_ids = ?',
      ['private', privateUserIds]
    );

    if (existingRows.length > 0) {
      return new ChatGroup(existingRows[0]);
    }

    // 不存在则创建新的私聊群组
    const groupData = {
      group_name: `私聊_${privateUserIds}`,
      group_avatar: null,
      description: '私聊会话',
      group_type: 'private',
      private_user_ids: privateUserIds
    };

    const newGroup = await this.create(groupData);

    // 自动添加两个用户为群组成员
    const GroupMember = require('./GroupMember');
    await GroupMember.create({
      group_id: newGroup.id,
      user_id: smallerId,
      role: 'member'
    });
    await GroupMember.create({
      group_id: newGroup.id,
      user_id: largerId,
      role: 'member'
    });

    return newGroup;
  }

  /**
   * 获取用户的私聊列表
   * @param {number} userId 用户ID
   * @returns {Promise<Array<Object>>}
   */
  static async getUserPrivateChats(userId) {
    const rows = await query(`
      SELECT cg.*,
             u1.id as other_user_id,
             u1.nickname as other_user_nickname,
             u1.avatar_url as other_user_avatar,
             (SELECT content FROM messages WHERE group_id = cg.id ORDER BY created_at DESC LIMIT 1) as last_message,
             (SELECT created_at FROM messages WHERE group_id = cg.id ORDER BY created_at DESC LIMIT 1) as last_message_time
      FROM chat_groups cg
      INNER JOIN group_members gm ON cg.id = gm.group_id
      INNER JOIN group_members gm2 ON cg.id = gm2.group_id AND gm2.user_id != ?
      INNER JOIN users u1 ON gm2.user_id = u1.id
      WHERE cg.group_type = 'private' AND gm.user_id = ?
      ORDER BY last_message_time DESC
    `, [userId, userId]);

    return rows.map(row => ({
      id: row.id,
      group_name: row.group_name,
      group_type: row.group_type,
      other_user: {
        id: row.other_user_id,
        nickname: row.other_user_nickname,
        avatar_url: row.other_user_avatar
      },
      last_message: row.last_message,
      last_message_time: row.last_message_time,
      created_at: row.created_at
    }));
  }

  /**
   * 获取群聊列表（排除私聊）
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  static async getGroupList(options = {}) {
    const { page = 1, pageSize = 20 } = options;
    const offset = (page - 1) * pageSize;

    // 获取总数（只统计群聊）
    const countRows = await query(
      'SELECT COUNT(*) as total FROM chat_groups WHERE group_type = ?',
      ['group']
    );
    const total = countRows[0].total;

    // 获取群组列表（只获取群聊）
    const rows = await query(
      `SELECT * FROM chat_groups WHERE group_type = ? ORDER BY id ASC LIMIT ${parseInt(pageSize)} OFFSET ${parseInt(offset)}`,
      ['group']
    );

    const groups = rows.map(row => new ChatGroup(row));

    return {
      data: groups,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }

  /**
   * 转换为JSON对象
   * @returns {Object}
   */
  toJSON() {
    return {
      id: this.id,
      group_name: this.group_name,
      group_avatar: this.group_avatar,
      description: this.description,
      group_type: this.group_type,
      private_user_ids: this.private_user_ids,
      created_at: this.created_at
    };
  }
}

module.exports = ChatGroup;
