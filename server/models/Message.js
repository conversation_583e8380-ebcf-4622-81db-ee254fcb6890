const { query, execute } = require('../config/db');

/**
 * 消息模型
 */
class Message {
  constructor(data) {
    this.id = data.id;
    this.group_id = data.group_id;
    this.sender_id = data.sender_id;
    this.message_type = data.message_type;
    this.content = data.content;
    this.created_at = data.created_at;
  }

  /**
   * 发送消息
   * @param {Object} messageData 消息数据
   * @returns {Promise<Message>}
   */
  static async create(messageData) {
    const { group_id, sender_id, message_type = 'text', content } = messageData;
    
    const [result] = await execute(
      'INSERT INTO messages (group_id, sender_id, message_type, content) VALUES (?, ?, ?, ?)',
      [group_id, sender_id, message_type, content]
    );

    const rows = await query(
      'SELECT * FROM messages WHERE id = ?',
      [result.insertId]
    );

    return new Message(rows[0]);
  }

  /**
   * 根据ID查找消息
   * @param {number} id 消息ID
   * @returns {Promise<Message|null>}
   */
  static async findById(id) {
    const rows = await query(
      'SELECT * FROM messages WHERE id = ?',
      [id]
    );

    return rows.length > 0 ? new Message(rows[0]) : null;
  }

  /**
   * 获取群组消息列表
   * @param {number} groupId 群组ID
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  static async getGroupMessages(groupId, options = {}) {
    const { page = 1, pageSize = 50, lastMessageId } = options;
    const pageNum = parseInt(page) || 1;
    const pageSizeNum = parseInt(pageSize) || 50;
    const offset = (pageNum - 1) * pageSizeNum;
    const groupIdNum = parseInt(groupId);

    if (isNaN(groupIdNum)) {
      throw new Error('Invalid groupId');
    }

    let whereClause = 'WHERE m.group_id = ?';
    let params = [groupIdNum];

    // 如果提供了lastMessageId，则获取比它更新的消息
    if (lastMessageId && !isNaN(parseInt(lastMessageId))) {
      whereClause += ' AND m.id > ?';
      params.push(parseInt(lastMessageId));
    }

    // 获取总数
    const countRows = await query(
      `SELECT COUNT(*) as total FROM messages m ${whereClause}`,
      params
    );
    const total = countRows[0].total;

    // 获取消息列表（包含发送者信息）
    // 注意：MySQL的LIMIT和OFFSET需要使用字符串拼接而不是参数绑定
    const rows = await query(`
      SELECT m.*, u.nickname, u.avatar_url
      FROM messages m
      LEFT JOIN users u ON m.sender_id = u.id
      ${whereClause}
      ORDER BY m.created_at DESC
      LIMIT ${pageSizeNum} OFFSET ${offset}
    `, params);

    const messages = rows.map(row => ({
      id: row.id,
      group_id: row.group_id,
      sender_id: row.sender_id,
      message_type: row.message_type,
      content: row.content,
      created_at: row.created_at,
      sender: {
        id: row.sender_id,
        nickname: row.nickname || '未知用户',
        avatar_url: row.avatar_url
      }
    }));

    return {
      data: messages,
      pagination: {
        page: pageNum,
        pageSize: pageSizeNum,
        total,
        totalPages: Math.ceil(total / pageSizeNum)
      }
    };
  }

  /**
   * 获取最新消息（用于轮询）
   * @param {number} groupId 群组ID
   * @param {number} lastMessageId 最后一条消息ID
   * @returns {Promise<Array>}
   */
  static async getNewMessages(groupId, lastMessageId = 0) {
    const rows = await query(`
      SELECT m.*, u.nickname, u.avatar_url
      FROM messages m
      LEFT JOIN users u ON m.sender_id = u.id
      WHERE m.group_id = ? AND m.id > ?
      ORDER BY m.created_at ASC
    `, [parseInt(groupId), parseInt(lastMessageId)]);

    return rows.map(row => ({
      id: row.id,
      group_id: row.group_id,
      sender_id: row.sender_id,
      message_type: row.message_type,
      content: row.content,
      created_at: row.created_at,
      sender: {
        id: row.sender_id,
        nickname: row.nickname || '未知用户',
        avatar_url: row.avatar_url
      }
    }));
  }

  /**
   * 获取用户在所有群组的最新消息
   * @param {number} userId 用户ID
   * @param {number} limit 限制数量
   * @returns {Promise<Array>}
   */
  static async getUserLatestMessages(userId, limit = 20) {
    const rows = await query(`
      SELECT m.*, u.nickname, u.avatar_url, cg.group_name
      FROM messages m
      LEFT JOIN users u ON m.sender_id = u.id
      LEFT JOIN chat_groups cg ON m.group_id = cg.id
      WHERE m.group_id IN (
        SELECT group_id FROM group_members WHERE user_id = ?
      )
      ORDER BY m.created_at DESC
      LIMIT ?
    `, [userId, limit]);

    return rows.map(row => ({
      id: row.id,
      group_id: row.group_id,
      sender_id: row.sender_id,
      message_type: row.message_type,
      content: row.content,
      created_at: row.created_at,
      sender: {
        id: row.sender_id,
        nickname: row.nickname || '未知用户',
        avatar_url: row.avatar_url
      },
      group: {
        group_name: row.group_name
      }
    }));
  }

  /**
   * 删除消息
   * @param {number} id 消息ID
   * @returns {Promise<boolean>}
   */
  static async delete(id) {
    const [result] = await execute(
      'DELETE FROM messages WHERE id = ?',
      [id]
    );

    return result.affectedRows > 0;
  }

  /**
   * 创建系统消息
   * @param {number} groupId 群组ID
   * @param {string} content 消息内容
   * @returns {Promise<Message>}
   */
  static async createSystemMessage(groupId, content) {
    return await this.create({
      group_id: groupId,
      sender_id: 0, // 系统消息使用0作为发送者ID
      message_type: 'system',
      content: content
    });
  }

  /**
   * 转换为JSON对象
   * @returns {Object}
   */
  toJSON() {
    return {
      id: this.id,
      group_id: this.group_id,
      sender_id: this.sender_id,
      message_type: this.message_type,
      content: this.content,
      created_at: this.created_at
    };
  }
}

module.exports = Message;
