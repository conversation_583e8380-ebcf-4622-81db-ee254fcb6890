const express = require('express');
const router = express.Router();
const Administrator = require('../models/AdministratorPrisma');
const UserPrisma = require('../models/UserPrisma');
const ListingPrisma = require('../models/ListingPrisma');
const PackagePrisma = require('../models/PackagePrisma');
const OrderPrisma = require('../models/OrderPrisma');
const ChatGroupPrisma = require('../models/ChatGroupPrisma');
const GroupMemberPrisma = require('../models/GroupMemberPrisma');
const MessagePrisma = require('../models/MessagePrisma');
const { authenticateAdmin, generateAdminToken } = require('../middleware/auth');
const ResponseHelper = require('../utils/response');
const Validator = require('../utils/validator');

/**
 * 管理员登录
 * POST /api/v1/admin/login
 */
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    // 验证必填字段
    if (!username || !password) {
      return ResponseHelper.validationError(res, ['用户名和密码不能为空']);
    }

    // 查找管理员
    const admin = await Administrator.findByUsername(username);
    if (!admin) {
      return ResponseHelper.unauthorized(res, '用户名或密码错误');
    }

    // 验证密码
    const isValidPassword = await admin.verifyPassword(password);
    if (!isValidPassword) {
      return ResponseHelper.unauthorized(res, '用户名或密码错误');
    }

    // 生成token
    const token = generateAdminToken(admin);

    ResponseHelper.success(res, {
      admin: admin.toJSON(),
      token
    }, '登录成功');

  } catch (error) {
    console.error('管理员登录错误:', error);
    ResponseHelper.serverError(res, '登录失败', error);
  }
});

/**
 * 获取管理员信息
 * GET /api/v1/admin/me
 */
router.get('/me', authenticateAdmin, async (req, res) => {
  try {
    const admin = await Administrator.findById(req.admin.id);
    if (!admin) {
      return ResponseHelper.notFound(res, '管理员不存在');
    }

    ResponseHelper.success(res, admin.toJSON(), '获取管理员信息成功');

  } catch (error) {
    console.error('获取管理员信息错误:', error);
    ResponseHelper.serverError(res, '获取管理员信息失败', error);
  }
});

/**
 * 获取仪表盘统计数据
 * GET /api/v1/admin/dashboard/stats
 */
router.get('/dashboard/stats', authenticateAdmin, async (req, res) => {
  try {
    // 获取用户统计
    const userStats = await UserPrisma.getStats();

    // 获取挂牌信息统计
    const listingStats = await ListingPrisma.getStats();

    const stats = {
      users: userStats,
      listings: listingStats,
      orders: {
        total: 0,
        today: 0
      },
      revenue: {
        total: 0,
        today: 0
      }
    };

    ResponseHelper.success(res, stats, '获取统计数据成功');

  } catch (error) {
    console.error('获取统计数据错误:', error);
    ResponseHelper.serverError(res, '获取统计数据失败', error);
  }
});

/**
 * 创建管理员
 * POST /api/v1/admin/administrators
 */
router.post('/administrators', authenticateAdmin, async (req, res) => {
  try {
    const { username, password } = req.body;

    // 验证必填字段
    if (!username || !password) {
      return ResponseHelper.validationError(res, ['用户名和密码不能为空']);
    }

    // 验证用户名长度
    if (username.length < 3 || username.length > 50) {
      return ResponseHelper.validationError(res, ['用户名长度必须在3-50个字符之间']);
    }

    // 验证密码长度
    if (password.length < 6) {
      return ResponseHelper.validationError(res, ['密码长度不能少于6个字符']);
    }

    // 检查用户名是否已存在
    const existingAdmin = await Administrator.findByUsername(username);
    if (existingAdmin) {
      return ResponseHelper.validationError(res, ['用户名已存在']);
    }

    // 创建管理员
    const newAdmin = await Administrator.create({ username, password });

    ResponseHelper.success(res, newAdmin.toJSON(), '创建管理员成功');

  } catch (error) {
    console.error('创建管理员错误:', error);
    ResponseHelper.serverError(res, '创建管理员失败', error);
  }
});

/**
 * 获取管理员列表
 * GET /api/v1/admin/administrators
 */
router.get('/administrators', authenticateAdmin, async (req, res) => {
  try {
    const { page = 1, pageSize = 10 } = req.query;
    
    const result = await Administrator.getList({ page, pageSize });
    
    ResponseHelper.paginated(res, result.data.map(admin => admin.toJSON()), result.pagination, '获取管理员列表成功');

  } catch (error) {
    console.error('获取管理员列表错误:', error);
    ResponseHelper.serverError(res, '获取管理员列表失败', error);
  }
});

/**
 * 删除管理员
 * DELETE /api/v1/admin/administrators/:id
 */
router.delete('/administrators/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const currentAdminId = req.admin.id;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的管理员ID']);
    }

    const adminId = parseInt(id);

    // 不能删除自己
    if (adminId === currentAdminId) {
      return ResponseHelper.forbidden(res, '不能删除自己的账号');
    }

    // 检查管理员是否存在
    const admin = await Administrator.findById(adminId);
    if (!admin) {
      return ResponseHelper.notFound(res, '管理员不存在');
    }

    // 删除管理员
    const deleted = await Administrator.delete(adminId);
    if (!deleted) {
      return ResponseHelper.serverError(res, '删除失败');
    }

    ResponseHelper.success(res, null, '删除管理员成功');

  } catch (error) {
    console.error('删除管理员错误:', error);
    ResponseHelper.serverError(res, '删除管理员失败', error);
  }
});

/**
 * 获取用户列表（管理员功能）
 * GET /api/v1/admin/users
 */
router.get('/users', authenticateAdmin, async (req, res) => {
  try {
    const { page = 1, pageSize = 10, status, search } = req.query;

    const result = await UserPrisma.getList({ page, pageSize, status, search });

    ResponseHelper.paginated(res, result.data.map(user => user.toJSON()), result.pagination, '获取用户列表成功');

  } catch (error) {
    console.error('获取用户列表错误:', error);
    ResponseHelper.serverError(res, '获取用户列表失败', error);
  }
});

/**
 * 获取用户详情（管理员功能）
 * GET /api/v1/admin/users/:id
 */
router.get('/users/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的用户ID']);
    }

    const userId = parseInt(id);

    // 查找用户
    const user = await UserPrisma.findById(userId);
    if (!user) {
      return ResponseHelper.notFound(res, '用户不存在');
    }

    ResponseHelper.success(res, user.toJSON(), '获取用户详情成功');

  } catch (error) {
    console.error('获取用户详情错误:', error);
    ResponseHelper.serverError(res, '获取用户详情失败', error);
  }
});

/**
 * 激活/停用用户
 * PUT /api/v1/admin/users/:id/status
 */
router.put('/users/:id/status', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的用户ID']);
    }

    // 验证状态值
    if (!status || !['active', 'inactive'].includes(status)) {
      return ResponseHelper.validationError(res, ['状态值必须是 active 或 inactive']);
    }

    const userId = parseInt(id);

    // 查找用户
    const user = await UserPrisma.findById(userId);
    if (!user) {
      return ResponseHelper.notFound(res, '用户不存在');
    }

    // 更新用户状态
    const updatedUser = await UserPrisma.update(userId, { status });

    ResponseHelper.success(res, updatedUser.toJSON(), '用户状态更新成功');

  } catch (error) {
    console.error('更新用户状态错误:', error);
    ResponseHelper.serverError(res, '更新用户状态失败', error);
  }
});

/**
 * 修改用户发布额度
 * PUT /api/v1/admin/users/:id/credits
 */
router.put('/users/:id/credits', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { credits } = req.body;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的用户ID']);
    }

    // 验证额度值
    if (!credits || isNaN(parseInt(credits)) || parseInt(credits) <= 0) {
      return ResponseHelper.validationError(res, ['额度必须是大于0的数字']);
    }

    const userId = parseInt(id);
    const creditsToAdd = parseInt(credits);

    // 查找用户
    const user = await UserPrisma.findById(userId);
    if (!user) {
      return ResponseHelper.notFound(res, '用户不存在');
    }

    // 增加用户额度
    const newCredits = user.publishing_credits + creditsToAdd;
    const updatedUser = await UserPrisma.update(userId, { publishing_credits: newCredits });

    ResponseHelper.success(res, updatedUser.toJSON(), '用户额度修改成功');

  } catch (error) {
    console.error('修改用户额度错误:', error);
    ResponseHelper.serverError(res, '修改用户额度失败', error);
  }
});

/**
 * 获取信息列表（管理员功能）
 * GET /api/v1/admin/listings
 */
router.get('/listings', authenticateAdmin, async (req, res) => {
  try {
    const { page = 1, pageSize = 10, status, listing_type, search } = req.query;

    const result = await ListingPrisma.getList({ page, pageSize, status, listing_type, search });

    ResponseHelper.paginated(res, result.data.map(listing => listing.toJSON()), result.pagination, '获取信息列表成功');

  } catch (error) {
    console.error('获取信息列表错误:', error);
    ResponseHelper.serverError(res, '获取信息列表失败', error);
  }
});

/**
 * 获取信息详情（管理员功能）
 * GET /api/v1/admin/listings/:id
 */
router.get('/listings/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的信息ID']);
    }

    const listingId = parseInt(id);

    // 查找信息
    const listing = await ListingPrisma.findById(listingId);
    if (!listing) {
      return ResponseHelper.notFound(res, '信息不存在');
    }

    ResponseHelper.success(res, listing.toJSON(), '获取信息详情成功');

  } catch (error) {
    console.error('获取信息详情错误:', error);
    ResponseHelper.serverError(res, '获取信息详情失败', error);
  }
});

/**
 * 更新信息状态（管理员功能）
 * PUT /api/v1/admin/listings/:id/status
 */
router.put('/listings/:id/status', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的信息ID']);
    }

    // 验证状态值
    if (!status || !['在售', '已售', '下架'].includes(status)) {
      return ResponseHelper.validationError(res, ['状态值必须是 在售、已售 或 下架']);
    }

    const listingId = parseInt(id);

    // 查找信息
    const listing = await ListingPrisma.findById(listingId);
    if (!listing) {
      return ResponseHelper.notFound(res, '信息不存在');
    }

    // 更新信息状态
    const updatedListing = await ListingPrisma.update(listingId, { status });

    ResponseHelper.success(res, updatedListing.toJSON(), '信息状态更新成功');

  } catch (error) {
    console.error('更新信息状态错误:', error);
    ResponseHelper.serverError(res, '更新信息状态失败', error);
  }
});

/**
 * 删除信息（管理员功能）
 * DELETE /api/v1/admin/listings/:id
 */
router.delete('/listings/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的信息ID']);
    }

    const listingId = parseInt(id);

    // 查找信息
    const listing = await ListingPrisma.findById(listingId);
    if (!listing) {
      return ResponseHelper.notFound(res, '信息不存在');
    }

    // 删除信息
    const deleted = await ListingPrisma.delete(listingId);
    if (!deleted) {
      return ResponseHelper.serverError(res, '删除失败');
    }

    ResponseHelper.success(res, null, '删除信息成功');

  } catch (error) {
    console.error('删除信息错误:', error);
    ResponseHelper.serverError(res, '删除信息失败', error);
  }
});

/**
 * 获取套餐列表（管理员功能）
 * GET /api/v1/admin/packages
 */
router.get('/packages', authenticateAdmin, async (req, res) => {
  try {
    const { page = 1, pageSize = 10, is_active, search } = req.query;

    const result = await PackagePrisma.getList({ page, pageSize, is_active, search });

    ResponseHelper.paginated(res, result.data.map(pkg => pkg.toJSON()), result.pagination, '获取套餐列表成功');

  } catch (error) {
    console.error('获取套餐列表错误:', error);
    ResponseHelper.serverError(res, '获取套餐列表失败', error);
  }
});

/**
 * 获取套餐详情（管理员功能）
 * GET /api/v1/admin/packages/:id
 */
router.get('/packages/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的套餐ID']);
    }

    const packageId = parseInt(id);

    // 查找套餐
    const pkg = await PackagePrisma.findById(packageId);
    if (!pkg) {
      return ResponseHelper.notFound(res, '套餐不存在');
    }

    ResponseHelper.success(res, pkg.toJSON(), '获取套餐详情成功');

  } catch (error) {
    console.error('获取套餐详情错误:', error);
    ResponseHelper.serverError(res, '获取套餐详情失败', error);
  }
});

/**
 * 创建套餐（管理员功能）
 * POST /api/v1/admin/packages
 */
router.post('/packages', authenticateAdmin, async (req, res) => {
  try {
    const { title, description, price, credits_amount, is_active = true } = req.body;

    // 验证必填字段
    if (!title || !price || !credits_amount) {
      return ResponseHelper.validationError(res, ['套餐名称、价格和条数不能为空']);
    }

    // 验证数据类型
    if (isNaN(parseFloat(price)) || parseFloat(price) <= 0) {
      return ResponseHelper.validationError(res, ['价格必须是大于0的数字']);
    }

    if (isNaN(parseInt(credits_amount)) || parseInt(credits_amount) <= 0) {
      return ResponseHelper.validationError(res, ['条数必须是大于0的整数']);
    }

    // 创建套餐
    const newPackage = await PackagePrisma.create({
      title,
      description,
      price: parseFloat(price),
      credits_amount: parseInt(credits_amount),
      is_active
    });

    ResponseHelper.success(res, newPackage.toJSON(), '创建套餐成功', 201);

  } catch (error) {
    console.error('创建套餐错误:', error);
    ResponseHelper.serverError(res, '创建套餐失败', error);
  }
});

/**
 * 更新套餐（管理员功能）
 * PUT /api/v1/admin/packages/:id
 */
router.put('/packages/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, price, credits_amount, is_active } = req.body;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的套餐ID']);
    }

    const packageId = parseInt(id);

    // 查找套餐
    const pkg = await PackagePrisma.findById(packageId);
    if (!pkg) {
      return ResponseHelper.notFound(res, '套餐不存在');
    }

    // 构建更新数据
    const updateData = {};
    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (price !== undefined) {
      if (isNaN(parseFloat(price)) || parseFloat(price) <= 0) {
        return ResponseHelper.validationError(res, ['价格必须是大于0的数字']);
      }
      updateData.price = parseFloat(price);
    }
    if (credits_amount !== undefined) {
      if (isNaN(parseInt(credits_amount)) || parseInt(credits_amount) <= 0) {
        return ResponseHelper.validationError(res, ['条数必须是大于0的整数']);
      }
      updateData.credits_amount = parseInt(credits_amount);
    }
    if (is_active !== undefined) updateData.is_active = is_active;

    // 更新套餐
    const updatedPackage = await PackagePrisma.update(packageId, updateData);

    ResponseHelper.success(res, updatedPackage.toJSON(), '更新套餐成功');

  } catch (error) {
    console.error('更新套餐错误:', error);
    ResponseHelper.serverError(res, '更新套餐失败', error);
  }
});

/**
 * 删除套餐（管理员功能）
 * DELETE /api/v1/admin/packages/:id
 */
router.delete('/packages/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的套餐ID']);
    }

    const packageId = parseInt(id);

    // 查找套餐
    const pkg = await PackagePrisma.findById(packageId);
    if (!pkg) {
      return ResponseHelper.notFound(res, '套餐不存在');
    }

    // 删除套餐
    const deleted = await PackagePrisma.delete(packageId);
    if (!deleted) {
      return ResponseHelper.serverError(res, '删除失败');
    }

    ResponseHelper.success(res, null, '删除套餐成功');

  } catch (error) {
    console.error('删除套餐错误:', error);
    ResponseHelper.serverError(res, '删除套餐失败', error);
  }
});

/**
 * 更新套餐状态（管理员功能）
 * PUT /api/v1/admin/packages/:id/status
 */
router.put('/packages/:id/status', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的套餐ID']);
    }

    // 验证状态值
    if (is_active === undefined || typeof is_active !== 'boolean') {
      return ResponseHelper.validationError(res, ['状态值必须是布尔类型']);
    }

    const packageId = parseInt(id);

    // 查找套餐
    const pkg = await PackagePrisma.findById(packageId);
    if (!pkg) {
      return ResponseHelper.notFound(res, '套餐不存在');
    }

    // 更新套餐状态
    const updatedPackage = await PackagePrisma.update(packageId, { is_active });

    ResponseHelper.success(res, updatedPackage.toJSON(), '套餐状态更新成功');

  } catch (error) {
    console.error('更新套餐状态错误:', error);
    ResponseHelper.serverError(res, '更新套餐状态失败', error);
  }
});

/**
 * 获取订单列表（管理员功能）
 * GET /api/v1/admin/orders
 */
router.get('/orders', authenticateAdmin, async (req, res) => {
  try {
    const { page = 1, pageSize = 10, payment_status, search } = req.query;

    const result = await OrderPrisma.getList({ page, pageSize, payment_status, search });

    ResponseHelper.paginated(res, result.data.map(order => order.toJSON()), result.pagination, '获取订单列表成功');

  } catch (error) {
    console.error('获取订单列表错误:', error);
    ResponseHelper.serverError(res, '获取订单列表失败', error);
  }
});

/**
 * 获取订单详情（管理员功能）
 * GET /api/v1/admin/orders/:id
 */
router.get('/orders/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的订单ID']);
    }

    const orderId = parseInt(id);

    // 查找订单
    const order = await OrderPrisma.findById(orderId);
    if (!order) {
      return ResponseHelper.notFound(res, '订单不存在');
    }

    ResponseHelper.success(res, order.toJSON(), '获取订单详情成功');

  } catch (error) {
    console.error('获取订单详情错误:', error);
    ResponseHelper.serverError(res, '获取订单详情失败', error);
  }
});

/**
 * 更新订单状态（管理员功能）
 * PUT /api/v1/admin/orders/:id/status
 */
router.put('/orders/:id/status', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { payment_status } = req.body;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的订单ID']);
    }

    // 验证状态值
    if (!payment_status || !['pending', 'paid', 'failed', 'cancelled'].includes(payment_status)) {
      return ResponseHelper.validationError(res, ['状态值必须是 pending、paid、failed 或 cancelled']);
    }

    const orderId = parseInt(id);

    // 查找订单
    const order = await OrderPrisma.findById(orderId);
    if (!order) {
      return ResponseHelper.notFound(res, '订单不存在');
    }

    // 更新订单状态
    const updatedOrder = await OrderPrisma.update(orderId, { payment_status });

    ResponseHelper.success(res, updatedOrder.toJSON(), '订单状态更新成功');

  } catch (error) {
    console.error('更新订单状态错误:', error);
    ResponseHelper.serverError(res, '更新订单状态失败', error);
  }
});

/**
 * 获取群聊列表（管理员功能）
 * GET /api/v1/admin/chat-groups
 */
router.get('/chat-groups', authenticateAdmin, async (req, res) => {
  try {
    const { page = 1, pageSize = 10, search } = req.query;

    const result = await ChatGroupPrisma.getList({ page, pageSize, search });

    ResponseHelper.paginated(res, result.data.map(group => group.toJSON()), result.pagination, '获取群聊列表成功');

  } catch (error) {
    console.error('获取群聊列表错误:', error);
    ResponseHelper.serverError(res, '获取群聊列表失败', error);
  }
});

/**
 * 获取群聊详情（管理员功能）
 * GET /api/v1/admin/chat-groups/:id
 */
router.get('/chat-groups/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的群聊ID']);
    }

    const groupId = parseInt(id);

    // 查找群聊（包含成员信息）
    const group = await ChatGroupPrisma.findByIdWithMembers(groupId);
    if (!group) {
      return ResponseHelper.notFound(res, '群聊不存在');
    }

    ResponseHelper.success(res, group.toJSON(), '获取群聊详情成功');

  } catch (error) {
    console.error('获取群聊详情错误:', error);
    ResponseHelper.serverError(res, '获取群聊详情失败', error);
  }
});

/**
 * 创建群聊（管理员功能）
 * POST /api/v1/admin/chat-groups
 */
router.post('/chat-groups', authenticateAdmin, async (req, res) => {
  try {
    const { group_name, group_avatar, description } = req.body;

    // 验证必填字段
    if (!group_name || group_name.trim() === '') {
      return ResponseHelper.validationError(res, ['群名称不能为空']);
    }

    // 验证群名称长度
    if (group_name.length > 255) {
      return ResponseHelper.validationError(res, ['群名称不能超过255个字符']);
    }

    // 验证群头像URL长度
    if (group_avatar && group_avatar.length > 512) {
      return ResponseHelper.validationError(res, ['群头像URL不能超过512个字符']);
    }

    // 创建群聊
    const newGroup = await ChatGroupPrisma.create({
      group_name: group_name.trim(),
      group_avatar: group_avatar || null,
      description: description || null
    });

    ResponseHelper.success(res, newGroup.toJSON(), '创建群聊成功');

  } catch (error) {
    console.error('创建群聊错误:', error);
    ResponseHelper.serverError(res, '创建群聊失败', error);
  }
});

/**
 * 更新群聊信息（管理员功能）
 * PUT /api/v1/admin/chat-groups/:id
 */
router.put('/chat-groups/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { group_name, group_avatar, description } = req.body;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的群聊ID']);
    }

    const groupId = parseInt(id);

    // 查找群聊
    const group = await ChatGroupPrisma.findById(groupId);
    if (!group) {
      return ResponseHelper.notFound(res, '群聊不存在');
    }

    // 验证群名称
    if (group_name !== undefined) {
      if (!group_name || group_name.trim() === '') {
        return ResponseHelper.validationError(res, ['群名称不能为空']);
      }
      if (group_name.length > 255) {
        return ResponseHelper.validationError(res, ['群名称不能超过255个字符']);
      }
    }

    // 验证群头像URL长度
    if (group_avatar !== undefined && group_avatar && group_avatar.length > 512) {
      return ResponseHelper.validationError(res, ['群头像URL不能超过512个字符']);
    }

    // 准备更新数据
    const updateData = {};
    if (group_name !== undefined) updateData.group_name = group_name.trim();
    if (group_avatar !== undefined) updateData.group_avatar = group_avatar || null;
    if (description !== undefined) updateData.description = description || null;

    // 更新群聊信息
    const updatedGroup = await ChatGroupPrisma.update(groupId, updateData);

    ResponseHelper.success(res, updatedGroup.toJSON(), '更新群聊信息成功');

  } catch (error) {
    console.error('更新群聊信息错误:', error);
    ResponseHelper.serverError(res, '更新群聊信息失败', error);
  }
});

/**
 * 删除群聊（管理员功能）
 * DELETE /api/v1/admin/chat-groups/:id
 */
router.delete('/chat-groups/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的群聊ID']);
    }

    const groupId = parseInt(id);

    // 查找群聊
    const group = await ChatGroupPrisma.findById(groupId);
    if (!group) {
      return ResponseHelper.notFound(res, '群聊不存在');
    }

    // 删除群聊（会级联删除相关的成员和消息）
    await ChatGroupPrisma.delete(groupId);

    ResponseHelper.success(res, null, '删除群聊成功');

  } catch (error) {
    console.error('删除群聊错误:', error);
    ResponseHelper.serverError(res, '删除群聊失败', error);
  }
});

// ==================== 消息管理相关接口 ====================

/**
 * 获取群聊消息列表
 * GET /api/v1/admin/chat-groups/:groupId/messages
 */
router.get('/chat-groups/:groupId/messages', authenticateAdmin, async (req, res) => {
  try {
    const { groupId } = req.params;
    const {
      page = 1,
      pageSize = 20,
      messageType = null,
      search = null,
      senderId = null
    } = req.query;

    // 验证群聊是否存在
    const group = await ChatGroupPrisma.findById(groupId);
    if (!group) {
      return ResponseHelper.notFound(res, '群聊不存在');
    }

    // 获取消息列表
    const result = await MessagePrisma.getAdminGroupMessages(groupId, {
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      messageType,
      search,
      senderId
    });

    ResponseHelper.success(res, {
      data: result.data,
      pagination: result.pagination
    }, '获取群聊消息列表成功');

  } catch (error) {
    console.error('获取群聊消息列表错误:', error);
    ResponseHelper.serverError(res, '获取群聊消息列表失败', error);
  }
});

/**
 * 获取消息详情
 * GET /api/v1/admin/messages/:messageId
 */
router.get('/messages/:messageId', authenticateAdmin, async (req, res) => {
  try {
    const { messageId } = req.params;

    const message = await MessagePrisma.findById(messageId);
    if (!message) {
      return ResponseHelper.notFound(res, '消息不存在');
    }

    ResponseHelper.success(res, message.toJSON(), '获取消息详情成功');

  } catch (error) {
    console.error('获取消息详情错误:', error);
    ResponseHelper.serverError(res, '获取消息详情失败', error);
  }
});

/**
 * 删除单条消息
 * DELETE /api/v1/admin/messages/:messageId
 */
router.delete('/messages/:messageId', authenticateAdmin, async (req, res) => {
  try {
    const { messageId } = req.params;

    // 验证消息是否存在
    const message = await MessagePrisma.findById(messageId);
    if (!message) {
      return ResponseHelper.notFound(res, '消息不存在');
    }

    // 删除消息
    await MessagePrisma.delete(messageId);

    ResponseHelper.success(res, null, '删除消息成功');

  } catch (error) {
    console.error('删除消息错误:', error);
    ResponseHelper.serverError(res, '删除消息失败', error);
  }
});

/**
 * 批量删除消息
 * DELETE /api/v1/admin/messages
 */
router.delete('/messages', authenticateAdmin, async (req, res) => {
  try {
    const { messageIds } = req.body;

    // 验证参数
    if (!messageIds || !Array.isArray(messageIds) || messageIds.length === 0) {
      return ResponseHelper.validationError(res, ['请提供要删除的消息ID列表']);
    }

    // 批量删除消息
    const deletedCount = await MessagePrisma.deleteByIds(messageIds);

    ResponseHelper.success(res, { deletedCount }, `成功删除 ${deletedCount} 条消息`);

  } catch (error) {
    console.error('批量删除消息错误:', error);
    ResponseHelper.serverError(res, '批量删除消息失败', error);
  }
});

/**
 * 获取群聊消息统计
 * GET /api/v1/admin/chat-groups/:groupId/messages/stats
 */
router.get('/chat-groups/:groupId/messages/stats', authenticateAdmin, async (req, res) => {
  try {
    const { groupId } = req.params;

    // 验证群聊是否存在
    const group = await ChatGroupPrisma.findById(groupId);
    if (!group) {
      return ResponseHelper.notFound(res, '群聊不存在');
    }

    // 获取消息统计
    const stats = await MessagePrisma.getGroupMessageStats(groupId);

    ResponseHelper.success(res, stats, '获取群聊消息统计成功');

  } catch (error) {
    console.error('获取群聊消息统计错误:', error);
    ResponseHelper.serverError(res, '获取群聊消息统计失败', error);
  }
});

module.exports = router;
